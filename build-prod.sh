#!/bin/bash
set -euo pipefail

# 配置参数
REMOTE_USER="ubuntu"
REMOTE_HOSTS=("box-777-server01" "box-777-server02")
COMPOSE_FILE="/data/web/docker-compose.yml"

# 构建并推送镜像
docker build --no-cache -t lkeke/box777-web:v1.0 . || exit 1
echo "🔼 推送镜像到仓库..."
docker push lkeke/box777-web:v1.0 || exit 1
echo "✅ 镜像推送成功"

# 更新所有主机
for host in "${REMOTE_HOSTS[@]}"; do
    echo "🚀 更新 $host"
    ssh $REMOTE_USER@$host "
        docker-compose -f $COMPOSE_FILE down || true
        docker-compose -f $COMPOSE_FILE pull || exit 1
        docker-compose -f $COMPOSE_FILE up -d || exit 1
    " || exit 1
    echo "✅ $host 更新完成"
done

echo "🎉 所有主机更新成功"