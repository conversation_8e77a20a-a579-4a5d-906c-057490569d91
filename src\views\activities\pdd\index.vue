<script setup lang="ts">
import { ref, h, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import {
  ElTag,
  ElMessageBox,
  ElMessage,
  ElButton,
  ElPopconfirm,
} from "element-plus";
import type { PddUserActivityItem } from "@/service/api/model/pdd";
import { useTable, useTableOperate } from "@/hooks/common/table";
import {
  getPddUserActivityList,
  deletePddUserActivity,
  updatePddUserActivityStatus,
  getPddActivitySetDonet,
  getTaskData,
  getParticipateDetails,
  getPddId,
} from "@/service/api/pdd";
import { useAuth } from "@/hooks/business/auth";
import Search from "./modules/search.vue";
import ActivityFormDrawer from "./modules/activity-form-drawer.vue";
import ParticipantListDrawer from "./modules/participant-list-drawer.vue";
import RechargeListDrawer from "./modules/recharge-list-drawer.vue";
import SuccessListDrawer from "./modules/success-list-drawer.vue";
import RegisterListDrawer from "./modules/register-list-drawer.vue";
import BroadcastManagementDrawer from "./modules/broadcast-management-drawer.vue";
import TableHeaderOperation from "@/components/advanced/table-header-operation.vue";
import moment from "moment";
import { getBrazilDate, getBrazilTime, formatNumber } from "@/utils/format";

const { t } = useI18n();
const { hasAuth } = useAuth();

defineOptions({ name: "PddActivityList" });

// 定义活动状态枚举
enum ActivityStatus {
  IN_PROGRESS = 1, // 进行中
  CLOSED = 2, // 已关闭
  NOT_STARTED = 0, // 未开始
}

// 定义奖励类型枚举
enum BonusType {
  CASH = 1, // 现金
  BONUS = 2, // 赠金
  CASH_WITH_REQUIREMENT = 3, // 打码现金
}

// 定义活动状态配置
const activityStatusConfig = {
  [ActivityStatus.IN_PROGRESS]: {
    type: "success",
    text: "完成",
    color: "#67C23A",
    icon: "Loading",
    order: 1,
  },
  [ActivityStatus.CLOSED]: {
    type: "danger",
    text: "未完成",
    color: "#F56C6C",
    icon: "CircleClose",
    order: 2,
  },
  [ActivityStatus.NOT_STARTED]: {
    type: "info",
    text: "进行中",
    color: "#909399",
    icon: "Clock",
    order: 3,
  },
} as const;

// 定义奖励类型配置
const bonusTypeConfig = {
  [BonusType.CASH]: {
    type: "success",
    text: "现金",
    color: "#67C23A",
  },
  [BonusType.BONUS]: {
    type: "warning",
    text: "赠金",
    color: "#E6A23C",
  },
  [BonusType.CASH_WITH_REQUIREMENT]: {
    type: "info",
    text: "打码现金",
    color: "#909399",
  },
} as const;

// 定义活动时间状态
interface ActivityTimeStatus {
  isBeforeStart: boolean;
  isAfterEnd: boolean;
}

// 计算活动时间状态
const calculateActivityTimeStatus = (
  row: PddUserActivityItem,
): ActivityTimeStatus => {
  const now = getBrazilTime().getTime();
  const startTime = getBrazilDate(row.start_time).getTime();
  const endTime = getBrazilDate(row.end_time).getTime();

  return {
    isBeforeStart: now < startTime,
    isAfterEnd: now > endTime,
  };
};
const selectedActivityId = ref<number | undefined>(undefined);

// 根据状态和时间判断可用的操作
const getAvailableOperations = (row: PddUserActivityItem) => {
  const timeStatus = calculateActivityTimeStatus(row);
  const operations = [];

  switch (row.status) {
    case ActivityStatus.NOT_STARTED:
      // 未开始：活动时间结束前可以开启、编辑、删除
      if (!timeStatus.isAfterEnd) {
        operations.push("start", "edit", "delete");
      }
      operations.push("view");
      break;
    case ActivityStatus.IN_PROGRESS:
      // 进行中：活动时间内可以关闭、查看详情
      if (!timeStatus.isAfterEnd) {
        operations.push("stop");
      }
      operations.push("view");
      break;
    case ActivityStatus.CLOSED:
      // 已关闭：活动时间已过可以开启、编辑、删除；活动时间内可以编辑、删除
      if (timeStatus.isAfterEnd) {
        operations.push("start", "edit", "delete");
      } else {
        operations.push("edit", "delete");
      }
      operations.push("view");
      break;
    default:
      operations.push("view");
      break;
  }

  return operations;
};

const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams,
} = useTable<any>({
  apiFn: getTaskData,
  apiParams: {
    page: 1,
    size: 10,
    start_time: undefined,
    end_time: undefined,
    user_id: undefined,
    bonus_type: undefined,
    status: undefined,
    activity_id: undefined,
    // 排序参数
    support_sort: undefined,
    register_sort: undefined,
    first_recharge_sort: undefined,
    recharge_sort: undefined,
    recharge_amount_sort: undefined,
    bonus_amount_sort: undefined,
    accumulated_bonus_sort: undefined,
    current_recharge_amount_sort: undefined,
  },
  columns: () => [
    { type: "index", label: t("common.index"), minWidth: 60 },
    { prop: "user_id", label: "用户ID", minWidth: 80 },
    {
      prop: "assisted_num",
      label: "助力人数",
      minWidth: 100,
      sortable: "custom",
      // formatter: (row: any, column: any) => {
      //   return h(
      //     ElButton,
      //     {
      //       type: "primary",
      //       link: true,
      //       onClick: () => handleViewParticipants(row),
      //     },
      //     () => row.assisted_num,
      //   );
      // },
    },
    {
      prop: "register_num",
      label: "注册人数",
      minWidth: 100,
      sortable: "custom",
      // formatter: (row: any, column: any) => {
      //   return h(
      //     ElButton,
      //     {
      //       type: "primary",
      //       link: true,
      //       onClick: () => handleViewRegisters(row),
      //     },
      //     () => row.register_num,
      //   );
      // },
    },
    {
      prop: "first_recharge_num",
      label: "首充人数",
      minWidth: 100,
      sortable: "custom",
    },
    {
      prop: "recharge_num",
      label: "充值人数",
      minWidth: 100,
      sortable: "custom",
      // formatter: (row: any, column: any) => {
      //   return h(
      //     ElButton,
      //     {
      //       type: "primary",
      //       link: true,
      //       onClick: () => handleViewRecharges(row),
      //     },
      //     () => row.recharge_num,
      //   );
      // },
    },
    {
      prop: "recharge_amount",
      label: "充值金额",
      minWidth: 100,
      sortable: "custom",
      formatter: (row: any) => {
        return "R$" + formatNumber((row.recharge_amount || 0) / 100);
      },
    },
    {
      prop: "bonus_type",
      label: "奖金类型",
      minWidth: 100,
      formatter: (row: any) => {
        const bonusType =
          bonusTypeConfig[row.bonus_type as BonusType] ||
          bonusTypeConfig[BonusType.CASH];
        return h(
          ElTag,
          {
            type: bonusType.type,
            effect: "light",
            class: "status-tag",
          },
          () => bonusType.text,
        );
      },
    },
    {
      prop: "bonus_amount",
      label: "奖金",
      minWidth: 100,
      sortable: "custom",
      formatter: (row: any) => {
        return "R$ " + row.bonus_amount;
      },
    },
    {
      prop: "accumulated_bonus",
      label: "累积赠金",
      minWidth: 100,
      sortable: "custom",
      formatter: (row: any) => {
        return "R$ " + row.accumulated_bonus;
      },
    },
    {
      prop: "current_recharge_amount",
      label: "当前充值金额",
      minWidth: 130,
      sortable: "custom",
      formatter: (row: any) => {
        return "R$ " + formatNumber(row.current_recharge_amount);
      },
    },
    {
      prop: "status",
      label: "状态",
      minWidth: 120,
      formatter: (row: any) => {
        const bonusType =
          activityStatusConfig[row.status as ActivityStatus] ||
          activityStatusConfig[ActivityStatus.NOT_STARTED];
        return h(
          ElTag,
          {
            type: bonusType.type,
            effect: "light",
            class: "status-tag",
          },
          () => bonusType.text,
        );
      },
    },
    {
      prop: "start_time",
      label: "开始时间",
      minWidth: 160,
      formatter: (row: any) => {
        return row.start_time
          ? moment(getBrazilDate(row.start_time)).format("YYYY-MM-DD HH:mm:ss")
          : "-";
      },
    },
    {
      prop: "done_time",
      label: "完成时间",
      minWidth: 160,
      formatter: (row: any) => {
        return row.done_time
          ? moment(getBrazilDate(row.done_time)).format("YYYY-MM-DD HH:mm:ss")
          : "-";
      },
    },
    {
      prop: "operation",
      label: "操作",
      minWidth: 80,
      formatter: (row: any) =>
        row.status === 3
          ? h(
              ElButton,
              {
                type: "danger",
                link: true,
                onClick: () => handleComplete(row),
              },
              () => "直接完成", // 这里改成“直接完成”
            )
          : "",
    },
  ],
});

const {
  drawerVisible: activityFormDrawerVisible,
  operateType: activityFormDrawerMode,
  handleAdd: handleAddActivity,
  handleEdit,
  onDeleted,
} = useTableOperate<any>(data, getData);

const configId = ref<number>();

const participantListDrawerVisible = ref(false);
const participantListActivityId = ref<number>();
const rechargeListDrawerVisible = ref(false);
const rechargeListActivityId = ref<number>();
const successListDrawerVisible = ref(false);
const successListActivityId = ref<number>();
const registerListDrawerVisible = ref(false);
const registerListActivityId = ref<number>();
const broadcastManagementDrawerVisible = ref(false);
const broadcastActivityId = ref<number>();


const handleSearch = () => {
  getDataByPage();
};

const handleReset = () => {
  resetSearchParams();
};

// 处理表格排序
const handleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
  // 清除所有排序参数
  searchParams.support_sort = undefined;
  searchParams.register_sort = undefined;
  searchParams.first_recharge_sort = undefined;
  searchParams.recharge_sort = undefined;
  searchParams.recharge_amount_sort = undefined;
  searchParams.bonus_amount_sort = undefined;
  searchParams.accumulated_bonus_sort = undefined;
  searchParams.current_recharge_amount_sort = undefined;

  // 根据列名设置对应的排序参数
  if (order) {
    const sortValue = order === 'ascending' ? 'ASC' : 'DESC';

    switch (prop) {
      case 'assisted_num':
        searchParams.support_sort = sortValue;
        break;
      case 'register_num':
        searchParams.register_sort = sortValue;
        break;
      case 'first_recharge_num':
        searchParams.first_recharge_sort = sortValue;
        break;
      case 'recharge_num':
        searchParams.recharge_sort = sortValue;
        break;
      case 'recharge_amount':
        searchParams.recharge_amount_sort = sortValue;
        break;
      case 'bonus_amount':
        searchParams.bonus_amount_sort = sortValue;
        break;
      case 'accumulated_bonus':
        searchParams.accumulated_bonus_sort = sortValue;
        break;
      case 'current_recharge_amount':
        searchParams.current_recharge_amount_sort = sortValue;
        break;
    }
  }

  // 重新获取数据
  getDataByPage();
};

const handleFormSuccess = async () => {
  await queryPddId();
};
const handleComplete = async (row: PddUserActivityItem) => {
  try {
    // 调用你的“直接完成”API
    // await yourCompleteApi();
    await getPddActivitySetDonet(row.id, {});
    ElMessage.success("操作成功");
    // emit("refresh");
    getData();
    fetchParticipateDetails(); // 獲取參與詳情數據
  } catch (error) {
    ElMessage.error("操作失败");
  }
};

// 数据卡片区块数据
const cardStats = ref({
  participantCount: 0,
  participantSuccessCount: 0,
  helpUserCount: 0,
  newRegisterCount: 0,
  rechargeAmount: 0,
  initiatorRechargeAmount: 0,
  cashBonus: 0,
  giftBonus: 0,
  processGiftBonus: 0,
});

// 獲取參與詳情數據
const fetchParticipateDetails = async () => {
  try {
    const response = await getParticipateDetails({
      activity_id: selectedActivityId.value,
    });
    console.log(response);
    if (response?.data?.data) {
      const data = response.data.data;
      cardStats.value = {
        participantCount: data.participate_num || 0,
        participantSuccessCount: data.finish_num || 0,
        helpUserCount: data.participate_helping_num || 0,
        newRegisterCount: data.new_register_num || 0,
        rechargeAmount: data.participate_helping_recharge_amount || 0,
        initiatorRechargeAmount: data.start_user_recharge_amount || 0,
        cashBonus: data.bonus_amount || 0,
        giftBonus: data.gr_jin_bonus_amount || 0,
        processGiftBonus: data.process_gr_jin_bonus_amount || 0,
      };
    }
  } catch (error) {
    console.error("獲取參與詳情失敗:", error);
  }
};

// 打开活动配置
const handleActivitySetting = () => {
  console.log(2232);
  if (!selectedActivityId.value) {
    handleAddActivity();
  } else {
    handleEdit(selectedActivityId.value);
  }
};

// 在頁面加載時調用 getPddId
onMounted(async () => {
  await queryPddId();
});

const queryPddId = async () => {
  try {
    const response = await getPddId({});
    console.log("PDD ID:", response);
    if (response) {
      selectedActivityId.value = response.data.data.activity_id;
      searchParams.activity_id = selectedActivityId.value;
      await getData();
      await fetchParticipateDetails(); // 獲取參與詳情數據
    }
  } catch (error) {
    console.error("獲取 PDD ID 失敗:", error);
  }
};

defineExpose({
  configId,
  columns,
  data,
  loading,
  mobilePagination,
  handleSearch,
  handleReset,
  handleAddActivity,
  handleSortChange,
});
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <!-- 数据卡片区块加上 ElCard -->
    <ElCard class="card-stats-elcard" shadow="never">
      <div class="card-stats-wrapper">
        <div class="card-stat">
          <div class="card-stat-title">参与活动人数</div>
          <div class="card-stat-value">{{ cardStats.participantCount }}</div>
          <div class="card-stat-desc">
            完成人数：{{ cardStats.participantSuccessCount }}
          </div>
        </div>
        <div class="card-stat">
          <div class="card-stat-title">参与助力用户数</div>
          <div class="card-stat-value">{{ cardStats.helpUserCount }}</div>
          <div class="card-stat-desc">
            新注册用户数：{{ cardStats.newRegisterCount }}
          </div>
        </div>
        <div class="card-stat">
          <div class="card-stat-title">助力用户充值金额</div>
          <div class="card-stat-value">
           R$ {{ formatNumber((cardStats.rechargeAmount || 0) / 100) }}
          </div>
          <div class="card-stat-desc">
            发起人充值金额：R$ {{ formatNumber((cardStats.initiatorRechargeAmount || 0) / 100) }}
          </div>
        </div>
        <div class="card-stat">
          <div class="card-stat-title">现金奖金（含打码提现额度）</div>
          <div class="card-stat-value">R$ {{ cardStats.cashBonus }}</div>
          <div class="card-stat-desc">
            赠金奖金：R$ {{ cardStats.giftBonus }}　过程赠金：R$ {{
              cardStats.processGiftBonus
            }}
          </div>
        </div>
      </div>
    </ElCard>
    <Search
      v-model:model="searchParams"
      @reset="handleReset"
      @search="handleSearch"
    >
      <template #table-operation> </template>
    </Search>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="flex justify-end mb-2">
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          @refresh="getData"
        >
          <template #default>
            <ElButton
              v-if="hasAuth(1)"
              type="primary"
              @click="handleActivitySetting"
            >
              活动配置
            </ElButton>
          </template>
        </TableHeaderOperation>
      </div>

      <div class="h-[calc(100%-100px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @sort-change="handleSortChange"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
    </ElCard>

    <ActivityFormDrawer
      v-model:visible="activityFormDrawerVisible"
      :mode="activityFormDrawerMode"
      :activity-id="selectedActivityId"
      @success="handleFormSuccess"
    />
    <ParticipantListDrawer
      v-model:visible="participantListDrawerVisible"
      :activity-id="participantListActivityId"
    />
    <RechargeListDrawer
      v-model:visible="rechargeListDrawerVisible"
      :activity-id="rechargeListActivityId"
    />
    <SuccessListDrawer
      v-model:visible="successListDrawerVisible"
      :activity-id="successListActivityId"
    />
    <RegisterListDrawer
      v-model:visible="registerListDrawerVisible"
      :activity-id="registerListActivityId"
    />
    <BroadcastManagementDrawer
      v-model:visible="broadcastManagementDrawerVisible"
      :activity-id="broadcastActivityId"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;

  .ht50 {
    height: calc(100% - 50px);
  }
}

:deep(.el-drawer__body) {
  padding-top: 0;

  .p-20px {
    padding-top: 0;
  }
}

:deep(.buttons) {
  width: 160px;
  display: flex;
  align-items: center;
  margin: 0;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.status-tag) {
  display: inline-flex;
  align-items: center;
  padding: 0 8px;
  height: 24px;
  border-radius: 4px;
  font-size: 12px;

  .el-icon {
    margin-right: 4px;
  }
}

.card-stats-wrapper {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.card-stat {
  background: #f5f6f7;
  border-radius: 6px;
  padding: 20px 24px 16px 24px;
  min-width: 220px;
  flex: 1 1 220px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.card-stat-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.card-stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #222;
  margin-bottom: 8px;
}

.card-stat-desc {
  font-size: 14px;
  color: #666;
}

.card-stats-elcard {
  margin-bottom: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
</style>
